08:45:00.590 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
08:45:00.590 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 2908 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
08:45:00.597 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
08:45:03.225 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
08:45:03.227 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
08:45:03.228 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
08:45:03.313 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
08:45:16.912 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
08:45:18.423 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
08:45:33.063 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
08:45:33.066 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
08:45:33.066 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
08:45:33.066 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
08:45:33.066 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

08:45:33.066 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
08:45:33.066 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
08:45:33.066 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@54f2039e
08:45:33.219 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
08:45:33.219 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
08:45:33.223 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
08:45:34.271 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
08:45:34.698 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
08:45:34.713 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 34.539 seconds (JVM running for 35.444)
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
08:45:34.713 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
09:02:39.475 [http-nio-8081-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:36:41.004 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码已失效]
09:36:52.559 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
09:57:18.459 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 17104 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
09:57:18.462 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
09:57:18.463 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:57:23.172 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
09:57:23.176 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
09:57:23.176 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
09:57:23.265 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
09:57:36.130 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
09:57:36.851 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
09:57:40.233 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:57:40.243 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:57:40.243 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:57:40.244 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
09:57:40.245 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

09:57:40.245 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
09:57:40.246 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:57:40.246 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@210110e9
09:57:40.343 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
09:57:40.343 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
09:57:40.347 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
09:57:41.244 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
09:57:41.584 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
09:57:41.593 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 23.514 seconds (JVM running for 24.199)
09:57:41.594 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
09:57:41.594 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
09:57:41.594 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
09:57:41.594 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
09:57:41.595 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
09:57:41.595 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
09:57:41.596 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
09:57:41.596 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
09:57:41.596 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
09:57:41.596 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:18:05.451 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23476 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
10:18:05.455 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
10:18:05.459 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:07.989 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
10:18:07.992 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
10:18:07.992 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
10:18:08.075 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
10:18:20.946 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
10:18:21.734 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
10:18:37.523 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:18:37.535 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:18:37.535 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:18:37.540 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
10:18:37.540 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

10:18:37.540 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
10:18:37.540 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:18:37.540 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@7f75838c
10:18:37.649 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
10:18:37.650 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
10:18:37.651 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
10:18:38.815 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
10:18:39.272 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
10:18:39.287 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 34.186 seconds (JVM running for 34.671)
10:18:39.288 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
10:18:39.288 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
10:18:39.288 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
10:18:39.288 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
10:18:39.293 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
10:57:45.912 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:57:51.618 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:46:05.475 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 16692 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:46:05.475 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:46:05.478 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:46:08.031 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:46:08.034 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:46:08.034 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:46:08.124 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:46:09.945 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:46:10.643 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:46:13.843 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:46:13.854 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:46:13.854 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:46:13.855 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:46:13.856 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:46:13.856 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:46:13.856 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:46:13.856 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@20274b7a
12:46:13.950 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:46:13.950 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:46:13.953 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:46:14.816 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:46:15.177 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:46:15.190 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 10.064 seconds (JVM running for 10.55)
12:46:15.193 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:46:15.193 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:46:15.193 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:46:15.193 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:46:15.194 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:46:15.194 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:46:15.195 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:46:15.195 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:46:15.195 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:46:15.195 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:46:18.654 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:46:22.106 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
12:46:24.421 [schedule-pool-1] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Error][验证码错误]
12:46:28.828 [schedule-pool-2] INFO  sys-user - [run,55] - [127.0.0.1]内网IP[yidianadmin][Success][登录成功]
12:47:41.549 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 23016 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
12:47:41.553 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
12:47:41.553 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:47:44.127 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
12:47:44.130 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
12:47:44.131 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
12:47:44.210 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
12:47:45.950 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
12:47:46.663 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
12:47:49.824 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:47:49.832 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:47:49.833 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:47:49.834 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
12:47:49.834 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

12:47:49.835 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
12:47:49.835 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:47:49.835 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@67af25cc
12:47:49.914 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
12:47:49.915 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
12:47:49.917 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
12:47:50.747 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
12:47:51.102 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
12:47:51.113 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.915 seconds (JVM running for 10.389)
12:47:51.116 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
12:47:51.116 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
12:47:51.116 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
12:47:51.116 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
12:47:51.118 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
12:47:51.118 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
12:47:51.118 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
12:47:51.118 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
12:47:51.119 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
12:47:51.119 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
12:47:55.628 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:09:26.856 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 12444 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:09:26.859 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:09:26.859 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:09:29.355 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:09:29.358 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:09:29.359 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:09:29.444 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:09:31.221 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:09:31.929 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:09:34.938 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:09:34.948 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:09:34.948 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:09:34.949 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:09:34.950 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:09:34.950 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:09:34.950 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:09:34.951 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@23c22c6f
13:09:35.039 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:09:35.040 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:09:35.042 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:09:35.952 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:09:36.310 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:09:36.322 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.849 seconds (JVM running for 10.319)
13:09:36.325 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:09:36.326 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:09:36.326 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:09:36.326 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:09:36.328 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:09:36.328 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:09:36.328 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:09:36.329 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:09:36.329 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:09:36.329 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:09:39.024 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:24:00.474 [restartedMain] INFO  c.r.RuoYiApplication - [logStarting,55] - Starting RuoYiApplication using Java 24.0.2 on DESKTOP-A1MO1HT with PID 11256 (D:\code_project\java_project\loan\post-loan-backend-master\ruoyi-admin\target\classes started by admin in D:\code_project\java_project\loan)
13:24:00.478 [restartedMain] INFO  c.r.RuoYiApplication - [logStartupProfileInfo,686] - The following 1 profile is active: "druid"
13:24:00.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:24:02.882 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Initializing ProtocolHandler ["http-nio-8081"]
13:24:02.884 [restartedMain] INFO  o.a.c.c.StandardService - [log,168] - Starting service [Tomcat]
13:24:02.884 [restartedMain] INFO  o.a.c.c.StandardEngine - [log,168] - Starting Servlet engine: [Apache Tomcat/9.0.105]
13:24:02.977 [restartedMain] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring embedded WebApplicationContext
13:24:04.674 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-1} inited
13:24:05.399 [restartedMain] INFO  c.a.d.p.DruidDataSource - [init,1002] - {dataSource-2} inited
13:24:08.378 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:24:08.385 [restartedMain] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:24:08.385 [restartedMain] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:24:08.386 [restartedMain] INFO  o.q.s.RAMJobStore - [initialize,155] - RAMJobStore initialized.
13:24:08.387 [restartedMain] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

13:24:08.388 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
13:24:08.388 [restartedMain] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:24:08.388 [restartedMain] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@1f0b8659
13:24:08.461 [restartedMain] INFO  c.loan.util.AESUtil - [setIv,59] - AES初始向量已设置
13:24:08.463 [restartedMain] INFO  c.loan.util.AESUtil - [setSecretKey,50] - AES密钥已设置
13:24:08.466 [restartedMain] INFO  c.loan.util.SignUtil - [setSignKey,30] - 签名密钥已设置
13:24:09.274 [restartedMain] INFO  o.a.c.h.Http11NioProtocol - [log,168] - Starting ProtocolHandler ["http-nio-8081"]
13:24:09.617 [restartedMain] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler quartzScheduler_$_NON_CLUSTERED started.
13:24:09.627 [restartedMain] INFO  c.r.RuoYiApplication - [logStarted,61] - Started RuoYiApplication in 9.521 seconds (JVM running for 10.023)
13:24:09.629 [restartedMain] INFO  c.l.u.ConfigValidator - [run,22] - 开始验证第三方接口配置...
13:24:09.629 [restartedMain] INFO  c.l.u.ConfigValidator - [run,29] - ✓ 第三方接口配置验证通过
13:24:09.629 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,116] - === 第三方接口配置摘要 ===
13:24:09.629 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,117] - AES密钥长度: 32 位
13:24:09.630 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,118] - AES IV长度: 16 位
13:24:09.630 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,119] - 签名密钥长度: 31 位
13:24:09.631 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,120] - 允许的合作方: [daihou001, daihou002]
13:24:09.631 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,121] - 时间戳有效期: 5 分钟
13:24:09.631 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,122] - IP白名单启用: false
13:24:09.631 [restartedMain] INFO  c.l.u.ConfigValidator - [printConfigSummary,131] - ========================
13:24:24.026 [http-nio-8081-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,168] - Initializing Spring DispatcherServlet 'dispatcherServlet'
