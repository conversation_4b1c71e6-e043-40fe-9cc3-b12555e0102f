<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.car_order_examine.mapper.CarOrderExamineMapper">
    
    <resultMap type="CarOrderExamine" id="CarOrderExamineResult">
        <result property="id"    column="id"    />
        <result property="applyNo"    column="apply_no"    />
        <result property="createBy"    column="create_by"    />
        <result property="createDate"    column="create_date"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateDate"    column="update_date"    />
        <result property="transportationFee"    column="transportation_fee"    />
        <result property="towingFee"    column="towing_fee"    />
        <result property="trackerInstallationFee"    column="tracker_installation_fee"    />
        <result property="otherReimbursement"    column="other_reimbursement"    />
        <result property="totalMoney"    column="total_money"    />
        <result property="status"    column="status"    />
        <result property="approveTime"    column="approve_time"    />
        <result property="approvalHistory"    column="approval_history"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="reasons"    column="reasons"    />
        <result property="approveBy"    column="approve_by"    />
        <result property="approveRole"    column="approve_role"    />
        <result property="applicationTime"    column="application_time"    />
        <result property="applicationBy"    column="application_by"    />
    </resultMap>

    <sql id="selectCarOrderExamineVo">
        select id, apply_no, create_by, create_date, update_by, update_date, transportation_fee, towing_fee, tracker_installation_fee, other_reimbursement, total_money, status, approve_time, approval_history, approval_status, reasons, approve_by, approve_role, application_time, application_by from car_order_examine
    </sql>

    <select id="selectCarOrderExamineList" parameterType="CarOrderExamine" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        <where>
            <if test="applyNo != null  and applyNo != ''"> and apply_no = #{applyNo}</if>
            <if test="teamId != null "> and team_id = #{teamId}</if>
            <if test="garageId != null "> and garage_id = #{garageId}</if>
            <if test="keyStatus != null "> and key_status = #{keyStatus}</if>
            <if test="startTime != null and endTime != null">AND allocation_time BETWEEN #{startTime} AND #{endTime}</if>
            <if test="customerName != null  and customerName != ''"> and customer_name like concat('%', #{customerName}, '%')</if>
            <if test="mobilePhone != null  and mobilePhone != ''"> and mobile_phone = #{mobilePhone}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="plateNo != null  and plateNo != ''"> and plate_no = #{plateNo}</if>
            <if test="jgName != null  and jgName != ''"> and jg_name = #{jgName}</if>
            <if test="status != null"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectCarOrderExamineById" parameterType="String" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        where id = #{id}
    </select>

    <select id="selectCarOrderExamineByApplyNo" parameterType="String" resultMap="CarOrderExamineResult">
        <include refid="selectCarOrderExamineVo"/>
        where apply_no = #{applyNo}
    </select>



    <insert id="insertCarOrderExamine" parameterType="CarOrderExamine">
        insert into car_order_examine
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="applyNo != null">apply_no,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createDate != null">create_date,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateDate != null">update_date,</if>
            <if test="transportationFee != null">transportation_fee,</if>
            <if test="towingFee != null">towing_fee,</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee,</if>
            <if test="otherReimbursement != null">other_reimbursement,</if>
            <if test="totalMoney != null">total_money,</if>
            <if test="status != null">status,</if>
            <if test="approveTime != null">approve_time,</if>
            <if test="approvalHistory != null">approval_history,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="reasons != null">reasons,</if>
            <if test="approveBy != null">approve_by,</if>
            <if test="approveRole != null">approve_role,</if>
            <if test="applicationTime != null">application_time,</if>
            <if test="applicationBy != null">application_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="applyNo != null">#{applyNo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateDate != null">#{updateDate},</if>
            <if test="transportationFee != null">#{transportationFee},</if>
            <if test="towingFee != null">#{towingFee},</if>
            <if test="trackerInstallationFee != null">#{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">#{otherReimbursement},</if>
            <if test="totalMoney != null">#{totalMoney},</if>
            <if test="status != null">#{status},</if>
            <if test="approveTime != null">#{approveTime},</if>
            <if test="approvalHistory != null">#{approvalHistory},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="reasons != null">#{reasons},</if>
            <if test="approveBy != null">#{approveBy},</if>
            <if test="approveRole != null">#{approveRole},</if>
            <if test="applicationTime != null">#{applicationTime},</if>
            <if test="applicationBy != null">#{applicationBy},</if>
         </trim>
    </insert>

    <update id="updateCarOrderExamine" parameterType="CarOrderExamine">
        update car_order_examine
        <trim prefix="SET" suffixOverrides=",">
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateDate != null">update_date = #{updateDate},</if>
            <if test="transportationFee != null">transportation_fee = #{transportationFee},</if>
            <if test="towingFee != null">towing_fee = #{towingFee},</if>
            <if test="trackerInstallationFee != null">tracker_installation_fee = #{trackerInstallationFee},</if>
            <if test="otherReimbursement != null">other_reimbursement = #{otherReimbursement},</if>
            <if test="totalMoney != null">total_money = #{totalMoney},</if>
            <if test="status != null">status = #{status},</if>
            <if test="approveTime != null">approve_time = #{approveTime},</if>
            <if test="approvalHistory != null">approval_history = #{approvalHistory},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="reasons != null">reasons = #{reasons},</if>
            <if test="approveBy != null">approve_by = #{approveBy},</if>
            <if test="approveRole != null">approve_role = #{approveRole},</if>
            <if test="applicationTime != null">application_time = #{applicationTime},</if>
            <if test="applicationBy != null">application_by = #{applicationBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCarOrderExamineById" parameterType="String">
        delete from car_order_examine where id = #{id}
    </delete>

    <delete id="deleteCarOrderExamineByIds" parameterType="String">
        delete from car_order_examine where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>