{"remainingRequest": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue?vue&type=template&id=7cd31f38&scoped=true", "dependencies": [{"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\src\\views\\litigation\\litigation\\litigation_approval.vue", "mtime": 1754028691113}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1753353054666}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1753353053188}, {"path": "D:\\code_project\\java_project\\loan\\post-loan-backend-page\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1753353054255}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CjxkaXYgY2xhc3M9ImFwcC1jb250YWluZXIiPgogIDxlbC1mb3JtIDptb2RlbD0icXVlcnlQYXJhbXMiIHJlZj0icXVlcnlGb3JtIiBzaXplPSJzbWFsbCIgOmlubGluZT0idHJ1ZSIgdi1zaG93PSJzaG93U2VhcmNoIiBsYWJlbC13aWR0aD0iNjhweCI+CiAgICA8IS0tIDEuIOi0t+asvuS6uuWnk+WQjSAtLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IiIgcHJvcD0iY3VzdG9tZXJOYW1lIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuY3VzdG9tZXJOYW1lIgogICAgICAgIHBsYWNlaG9sZGVyPSLotLfmrL7kurrlp5PlkI0iCiAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgQGtleXVwLmVudGVyLm5hdGl2ZT0iaGFuZGxlUXVlcnkiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8IS0tIDIuIOi0t+asvuS6uui6q+S7veivgeWPtyAtLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IiIgcHJvcD0iY2VydElkIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuY2VydElkIgogICAgICAgIHBsYWNlaG9sZGVyPSLotLfmrL7kurrouqvku73or4Hlj7ciCiAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgQGtleXVwLmVudGVyLm5hdGl2ZT0iaGFuZGxlUXVlcnkiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8IS0tIDMuIOWHuuWNlea4oOmBkyAtLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IiIgcHJvcD0iamdOYW1lIj4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuamdOYW1lIgogICAgICAgIHBsYWNlaG9sZGVyPSLlh7rljZXmuKDpgZMiCiAgICAgICAgY2xlYXJhYmxlCiAgICAgICAgQGtleXVwLmVudGVyLm5hdGl2ZT0iaGFuZGxlUXVlcnkiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8IS0tIDQuIOaUvuasvumTtuihjCAtLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IiIgcHJvcD0ibGVuZGluZ0JhbmsiPgogICAgICA8ZWwtaW5wdXQKICAgICAgICB2LW1vZGVsPSJxdWVyeVBhcmFtcy5sZW5kaW5nQmFuayIKICAgICAgICBwbGFjZWhvbGRlcj0i5pS+5qy+6ZO26KGMIgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVF1ZXJ5IgogICAgICAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgPCEtLSA1LiDms5Xor4nnirbmgIEo6ZyA5ZGI546w5aSa57qnKSAtLT4KICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IiIgcHJvcD0ibGl0aWdhdGlvblN0YXR1cyI+CiAgICAgIDxlbC1jYXNjYWRlcgogICAgICAgIHYtbW9kZWw9InF1ZXJ5UGFyYW1zLmxpdGlnYXRpb25TdGF0dXMiCiAgICAgICAgOm9wdGlvbnM9ImxpdGlnYXRpb25TdGF0dXNPcHRpb25zIgogICAgICAgIDpwcm9wcz0ieyBleHBhbmRUcmlnZ2VyOiAnaG92ZXInLCB2YWx1ZTogJ3ZhbHVlJywgbGFiZWw6ICdsYWJlbCcsIGNoaWxkcmVuOiAnY2hpbGRyZW4nIH0iCiAgICAgICAgcGxhY2Vob2xkZXI9IuazleivieeKtuaAgSIKICAgICAgICBjbGVhcmFibGUKICAgICAgICBAY2hhbmdlPSJoYW5kbGVRdWVyeSIKICAgICAgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgIDwhLS0gNi4g55Sz6K+35Lq6IC0tPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0iIiBwcm9wPSJhcHBsaWNhdGlvbkJ5Ij4KICAgICAgPGVsLWlucHV0CiAgICAgICAgdi1tb2RlbD0icXVlcnlQYXJhbXMuYXBwbGljYXRpb25CeSIKICAgICAgICBwbGFjZWhvbGRlcj0i55Sz6K+35Lq6IgogICAgICAgIGNsZWFyYWJsZQogICAgICAgIEBrZXl1cC5lbnRlci5uYXRpdmU9ImhhbmRsZVF1ZXJ5IgogICAgICAvPgogICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgPCEtLSA3LiDotLnnlKjnsbvlnosgLS0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSIiIHByb3A9ImNvc3RDYXRlZ29yeSI+CiAgICAgIDxlbC1zZWxlY3Qgdi1tb2RlbD0icXVlcnlQYXJhbXMuY29zdENhdGVnb3J5IiBwbGFjZWhvbGRlcj0i6LS555So57G75Z6LIiBjbGVhcmFibGUgQGNoYW5nZT0iaGFuZGxlUXVlcnkiPgogICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuW+i+W4iOi0uSIgdmFsdWU9IuW+i+W4iOi0uSIgLz4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLor4norrzotLkiIHZhbHVlPSLor4norrzotLkiIC8+CiAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5L+d5YWo6LS5IiB2YWx1ZT0i5L+d5YWo6LS5IiAvPgogICAgICAgIDxlbC1vcHRpb24gbGFiZWw9IuaJp+ihjOi0uSIgdmFsdWU9IuaJp+ihjOi0uSIgLz4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlhbbku5botLnnlKgiIHZhbHVlPSLlhbbku5botLnnlKgiIC8+CiAgICAgIDwvZWwtc2VsZWN0PgogICAgPC9lbC1mb3JtLWl0ZW0+CgogICAgPCEtLSA4LiDlrqHmibnnirbmgIEgLS0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSIiIHByb3A9ImFwcHJvdmFsU3RhdHVzIj4KICAgICAgPGVsLXNlbGVjdCB2LW1vZGVsPSJxdWVyeVBhcmFtcy5hcHByb3ZhbFN0YXR1cyIgcGxhY2Vob2xkZXI9IuWuoeaJueeKtuaAgSIgY2xlYXJhYmxlIEBjaGFuZ2U9ImhhbmRsZVF1ZXJ5Ij4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLmnKrlrqHmoLgiIHZhbHVlPSIiIC8+CiAgICAgICAgPGVsLW9wdGlvbiBsYWJlbD0i5bey6YCa6L+HIiB2YWx1ZT0iMCIgLz4KICAgICAgICA8ZWwtb3B0aW9uIGxhYmVsPSLlt7Lmi5Lnu50iIHZhbHVlPSIxIiAvPgogICAgICA8L2VsLXNlbGVjdD4KICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgIDwhLS0gOS4g55Sz6K+35pe26Ze05Yy66Ze0IC0tPgogICAgPGVsLWZvcm0taXRlbSBsYWJlbD0iIiBwcm9wPSJkYXRlUmFuZ2UiPgogICAgICA8ZWwtZGF0ZS1waWNrZXIKICAgICAgICB2LW1vZGVsPSJkYXRlUmFuZ2UiCiAgICAgICAgdHlwZT0iZGF0ZXJhbmdlIgogICAgICAgIHJhbmdlLXNlcGFyYXRvcj0i6IezIgogICAgICAgIHN0YXJ0LXBsYWNlaG9sZGVyPSLnlLPor7flvIDlp4vml6XmnJ8iCiAgICAgICAgZW5kLXBsYWNlaG9sZGVyPSLnlLPor7fnu5PmnZ/ml6XmnJ8iCiAgICAgICAgZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICBAY2hhbmdlPSJoYW5kbGVEYXRlUmFuZ2VDaGFuZ2UiCiAgICAgIC8+CiAgICA8L2VsLWZvcm0taXRlbT4KCiAgICA8IS0tIDEwLiDlrqHmibnml7bpl7TljLrpl7QgLS0+CiAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSIiIHByb3A9ImFwcHJvdmFsRGF0ZVJhbmdlIj4KICAgICAgPGVsLWRhdGUtcGlja2VyCiAgICAgICAgdi1tb2RlbD0iYXBwcm92YWxEYXRlUmFuZ2UiCiAgICAgICAgdHlwZT0iZGF0ZXJhbmdlIgogICAgICAgIHJhbmdlLXNlcGFyYXRvcj0i6IezIgogICAgICAgIHN0YXJ0LXBsYWNlaG9sZGVyPSLlrqHmibnlvIDlp4vml6XmnJ8iCiAgICAgICAgZW5kLXBsYWNlaG9sZGVyPSLlrqHmibnnu5PmnZ/ml6XmnJ8iCiAgICAgICAgZm9ybWF0PSJ5eXl5LU1NLWRkIgogICAgICAgIHZhbHVlLWZvcm1hdD0ieXl5eS1NTS1kZCIKICAgICAgICBAY2hhbmdlPSJoYW5kbGVBcHByb3ZhbERhdGVSYW5nZUNoYW5nZSIKICAgICAgLz4KICAgIDwvZWwtZm9ybS1pdGVtPgoKICAgIDxlbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1idXR0b24gdHlwZT0icHJpbWFyeSIgaWNvbj0iZWwtaWNvbi1zZWFyY2giIHNpemU9Im1pbmkiIEBjbGljaz0iaGFuZGxlUXVlcnkiPuaQnOe0ojwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIGljb249ImVsLWljb24tcmVmcmVzaCIgc2l6ZT0ibWluaSIgQGNsaWNrPSJyZXNldFF1ZXJ5Ij7ph43nva48L2VsLWJ1dHRvbj4KICAgIDwvZWwtZm9ybS1pdGVtPgogIDwvZWwtZm9ybT4KCiAgPGVsLXJvdyA6Z3V0dGVyPSIxMCIgY2xhc3M9Im1iOCI+CiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0icHJpbWFyeSIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tcGx1cyIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIEBjbGljaz0iaGFuZGxlQWRkIgogICAgICAgIHYtaGFzUGVybWk9Ilsndm1fY2FyX29yZGVyOnZtX2Nhcl9vcmRlcjphZGQnXSIKICAgICAgPuaWsOWinjwvZWwtYnV0dG9uPgogICAgPC9lbC1jb2w+CiAgICA8ZWwtY29sIDpzcGFuPSIxLjUiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0ic3VjY2VzcyIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZWRpdCIKICAgICAgICBzaXplPSJtaW5pIgogICAgICAgIDpkaXNhYmxlZD0ic2luZ2xlIgogICAgICAgIEBjbGljaz0iaGFuZGxlQmF0Y2hFZGl0IgogICAgICAgIHYtaGFzUGVybWk9Ilsndm1fY2FyX29yZGVyOnZtX2Nhcl9vcmRlcjplZGl0J10iCiAgICAgID7kv67mlLk8L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICBwbGFpbgogICAgICAgIGljb249ImVsLWljb24tZGVsZXRlIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgOmRpc2FibGVkPSJtdWx0aXBsZSIKICAgICAgICBAY2xpY2s9ImhhbmRsZURlbGV0ZSIKICAgICAgICB2LWhhc1Blcm1pPSJbJ3ZtX2Nhcl9vcmRlcjp2bV9jYXJfb3JkZXI6cmVtb3ZlJ10iCiAgICAgID7liKDpmaQ8L2VsLWJ1dHRvbj4KICAgIDwvZWwtY29sPgogICAgPGVsLWNvbCA6c3Bhbj0iMS41Ij4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9Indhcm5pbmciCiAgICAgICAgcGxhaW4KICAgICAgICBpY29uPSJlbC1pY29uLWRvd25sb2FkIgogICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVFeHBvcnQiCiAgICAgICAgdi1oYXNQZXJtaT0iWyd2bV9jYXJfb3JkZXI6dm1fY2FyX29yZGVyOmV4cG9ydCddIgogICAgICA+5a+85Ye6PC9lbC1idXR0b24+CiAgICA8L2VsLWNvbD4KICA8L2VsLXJvdz4KCiAgPGVsLXRhYmxlIHYtbG9hZGluZz0ibG9hZGluZyIgOmRhdGE9InZtX2Nhcl9vcmRlckxpc3QiIEBzZWxlY3Rpb24tY2hhbmdlPSJoYW5kbGVTZWxlY3Rpb25DaGFuZ2UiIHJvdy1rZXk9ImlkIiBzdHlsZT0id2lkdGg6IDEwMCUiIGZsZXg9InJpZ2h0Ij4KICAgIDxlbC10YWJsZS1jb2x1bW4gdHlwZT0ic2VsZWN0aW9uIiB3aWR0aD0iNTUiIGFsaWduPSJjZW50ZXIiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLluo/lj7ciIGFsaWduPSJjZW50ZXIiIHR5cGU9ImluZGV4IiB3aWR0aD0iNjAiIGZpeGVkIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLnlLPor7fkuroiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcGxpY2F0aW9uQnkiIHdpZHRoPSIxMDAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmnIDmlrDnlLPor7fml7bpl7QiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcGxpY2F0aW9uVGltZSIgd2lkdGg9IjE1MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuahiOS7tui0n+i0o+S6uiIgYWxpZ249ImNlbnRlciIgcHJvcD0iY3VyYXRvciIgd2lkdGg9IjEwMCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaPkOS6pOasoeaVsCIgYWxpZ249ImNlbnRlciIgcHJvcD0ic3VibWlzc2lvbkNvdW50IiB3aWR0aD0iMTAwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5rOV6K+J54q25oCBIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJsaXRpZ2F0aW9uU3RhdHVzIiB3aWR0aD0iMTAwIj4KICAgICAgPHRlbXBsYXRlIHNsb3Qtc2NvcGU9InNjb3BlIj4KICAgICAgICA8c3Bhbj57e3Njb3BlLnJvdy5saXRpZ2F0aW9uU3RhdHVzID09ICcxJz8n5b6F56uL5qGIJzon5bey6YKu5a+EJ319PC9zcGFuPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLotLfmrL7kuroiIGFsaWduPSJjZW50ZXIiIHdpZHRoPSIxMDAiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxlbC1idXR0b24KICAgICAgICAgIHR5cGU9InRleHQiCiAgICAgICAgICBAY2xpY2s9Im9wZW5Vc2VySW5mbyhzY29wZS5yb3cpIgogICAgICAgICAgc3R5bGU9ImNvbG9yOiAjNDA5RUZGOyI+CiAgICAgICAgICB7eyBzY29wZS5yb3cuY3VzdG9tZXJOYW1lIH19CiAgICAgICAgPC9lbC1idXR0b24+CiAgICAgIDwvdGVtcGxhdGU+CiAgICA8L2VsLXRhYmxlLWNvbHVtbj4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWHuuWNlea4oOmBkyIgYWxpZ249ImNlbnRlciIgcHJvcD0iamdOYW1lIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5Zyw5Yy6IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJhcmVhIiB3aWR0aD0iODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmlL7mrL7pk7booYwiIGFsaWduPSJjZW50ZXIiIHByb3A9ImxlbmRpbmdCYW5rIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5rOV6Zmi5ZywIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJjb3VydExvY2F0aW9uIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6K+J6K685rOV6ZmiIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJjb21tb25QbGVhcyIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuW+i+W4iOi0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0ibGF3eWVyRmVlIiB3aWR0aD0iODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLor4norrzotLkiIGFsaWduPSJjZW50ZXIiIHByb3A9ImxpdGlnYXRpb25GZWUiIHdpZHRoPSI4MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuS/nemZqei0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0iaW5zdXJhbmNlIiB3aWR0aD0iODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLkv53lhajotLkiIGFsaWduPSJjZW50ZXIiIHByb3A9InByZXNlcnZhdGlvbkZlZSIgd2lkdGg9IjgwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5biD5o6n6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJzdXJ2ZWlsbGFuY2VGZWUiIHdpZHRoPSI4MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWFrOWRiui0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0iYW5ub3VuY2VtZW50RmVlIiB3aWR0aD0iODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLor4TkvLDotLkiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcHJhaXNhbEZlZSIgd2lkdGg9IjgwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5omn6KGM6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJleGVjdXRpb25GZWUiIHdpZHRoPSI4MCIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IueJueauiumAmumBk+i0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0ic3BlY2lhbENoYW5uZWxGZWVzIiB3aWR0aD0iMTAwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5pel5bi45oql6ZSAIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJvdGhlckFtb3VudHNPd2VkIiB3aWR0aD0iODAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmgLvotLnnlKgiIGFsaWduPSJjZW50ZXIiIHByb3A9InRvdGFsTW9uZXkiIHdpZHRoPSIxMDAiIC8+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlrqHmibnnirbmgIEiIGFsaWduPSJjZW50ZXIiIHByb3A9Im92ZXJhbGxBcHByb3ZhbFN0YXR1cyIgd2lkdGg9IjEyMCI+CiAgICAgIDx0ZW1wbGF0ZSBzbG90LXNjb3BlPSJzY29wZSI+CiAgICAgICAgPGVsLXRhZyB2LWlmPSJzY29wZS5yb3cub3ZlcmFsbEFwcHJvdmFsU3RhdHVzID09ICdOT1RfU1RBUlRFRCciIHR5cGU9ImluZm8iPuacquW8gOWni+WuoeaJuTwvZWwtdGFnPgogICAgICAgIDxlbC10YWcgdi1lbHNlLWlmPSJzY29wZS5yb3cub3ZlcmFsbEFwcHJvdmFsU3RhdHVzID09ICdQQVJUSUFMJyIgdHlwZT0id2FybmluZyI+6YOo5YiG5a6h5om5PC9lbC10YWc+CiAgICAgICAgPGVsLXRhZyB2LWVsc2UtaWY9InNjb3BlLnJvdy5vdmVyYWxsQXBwcm92YWxTdGF0dXMgPT0gJ0NPTVBMRVRFRCciIHR5cGU9InN1Y2Nlc3MiPuWFqOmDqOWuoeaJuTwvZWwtdGFnPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlrqHmibnml7bpl7QiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcHJvdmVUaW1lIiB3aWR0aD0iMTUwIiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5a6h5om55Lq66KeS6ImyIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJhcHByb3ZlUm9sZSIgLz4KICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWuoeaJueS6uiIgYWxpZ249ImNlbnRlciIgcHJvcD0iYXBwcm92ZUJ5IiAvPgogICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5pON5L2cIiBhbGlnbj0iY2VudGVyIiB3aWR0aD0iMTAwIiBjbGFzcy1uYW1lPSJzbWFsbC1wYWRkaW5nIGZpeGVkLXdpZHRoIiBmaXhlZD0icmlnaHQiPgogICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgIDxlbC1idXR0b24KICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICB0eXBlPSJ0ZXh0IgogICAgICAgICAgQGNsaWNrPSJoYW5kbGVVcGRhdGUoc2NvcGUucm93KSIKICAgICAgICAgIHYtaGFzUGVybWk9Ilsndm1fY2FyX29yZGVyOnZtX2Nhcl9vcmRlcjplZGl0J10iCiAgICAgICAgPuWuoeaJuTwvZWwtYnV0dG9uPgogICAgICA8L3RlbXBsYXRlPgogICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgPC9lbC10YWJsZT4KICAKICA8cGFnaW5hdGlvbgogICAgdi1zaG93PSJ0b3RhbD4wIgogICAgOnRvdGFsPSJ0b3RhbCIKICAgIDpwYWdlLnN5bmM9InF1ZXJ5UGFyYW1zLnBhZ2VOdW0iCiAgICA6bGltaXQuc3luYz0icXVlcnlQYXJhbXMucGFnZVNpemUiCiAgICBAcGFnaW5hdGlvbj0iZ2V0TGlzdCIKICAvPgoKICA8IS0tIOWuoeaJueWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nIHRpdGxlPSLms5Xor4notLnnlKjlrqHmibnor6bmg4UiIDp2aXNpYmxlLnN5bmM9Im9wZW4iIHdpZHRoPSIxMjAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGRpdiBjbGFzcz0iYXBwcm92YWwtaGVhZGVyIj4KICAgICAgPGVsLXJvdz4KICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgIDxzdHJvbmc+6LS35qy+5Lq677yaPC9zdHJvbmc+e3sgY3VycmVudFJlY29yZC5jdXN0b21lck5hbWUgfX0KICAgICAgICA8L2VsLWNvbD4KICAgICAgICA8ZWwtY29sIDpzcGFuPSI4Ij4KICAgICAgICAgIDxzdHJvbmc+5qGI5Lu26LSf6LSj5Lq677yaPC9zdHJvbmc+e3sgY3VycmVudFJlY29yZC5jdXJhdG9yIH19CiAgICAgICAgPC9lbC1jb2w+CiAgICAgICAgPGVsLWNvbCA6c3Bhbj0iOCI+CiAgICAgICAgICA8c3Ryb25nPuazlemZouWcsO+8mjwvc3Ryb25nPnt7IGN1cnJlbnRSZWNvcmQuY291cnRMb2NhdGlvbiB9fQogICAgICAgIDwvZWwtY29sPgogICAgICA8L2VsLXJvdz4KICAgIDwvZGl2PgoKICAgIDxkaXYgY2xhc3M9ImJhdGNoLWFwcHJvdmFsLXNlY3Rpb24iIHN0eWxlPSJtYXJnaW46IDIwcHggMDsiPgogICAgICA8ZWwtYnV0dG9uCiAgICAgICAgdHlwZT0ic3VjY2VzcyIKICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICA6ZGlzYWJsZWQ9InNlbGVjdGVkUmVjb3Jkcy5sZW5ndGggPT09IDAiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVCYXRjaEFwcHJvdmUoJzAnKSI+CiAgICAgICAg5om56YeP6YCa6L+HICh7eyBzZWxlY3RlZFJlY29yZHMubGVuZ3RoIH19KQogICAgICA8L2VsLWJ1dHRvbj4KICAgICAgPGVsLWJ1dHRvbgogICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICBzaXplPSJzbWFsbCIKICAgICAgICA6ZGlzYWJsZWQ9InNlbGVjdGVkUmVjb3Jkcy5sZW5ndGggPT09IDAiCiAgICAgICAgQGNsaWNrPSJoYW5kbGVCYXRjaEFwcHJvdmUoJzEnKSI+CiAgICAgICAg5om56YeP5ouS57udICh7eyBzZWxlY3RlZFJlY29yZHMubGVuZ3RoIH19KQogICAgICA8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgoKICAgIDxlbC10YWJsZQogICAgICA6ZGF0YT0ic3VibWlzc2lvblJlY29yZHMiCiAgICAgIEBzZWxlY3Rpb24tY2hhbmdlPSJoYW5kbGVSZWNvcmRTZWxlY3Rpb25DaGFuZ2UiCiAgICAgIHYtbG9hZGluZz0icmVjb3Jkc0xvYWRpbmciPgogICAgICA8ZWwtdGFibGUtY29sdW1uIHR5cGU9InNlbGVjdGlvbiIgd2lkdGg9IjU1IiBhbGlnbj0iY2VudGVyIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmj5DkuqTml7bpl7QiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcGxpY2F0aW9uVGltZSIgd2lkdGg9IjE1MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5o+Q5Lqk5Lq6IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJhcHBsaWNhdGlvbkJ5IiB3aWR0aD0iMTAwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlvovluIjotLkiIGFsaWduPSJjZW50ZXIiIHByb3A9Imxhd3llckZlZSIgd2lkdGg9IjgwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLor4norrzotLkiIGFsaWduPSJjZW50ZXIiIHByb3A9ImxpdGlnYXRpb25GZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5L+d5YWo6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJwcmVzZXJ2YXRpb25GZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5biD5o6n6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJzdXJ2ZWlsbGFuY2VGZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5YWs5ZGK6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJhbm5vdW5jZW1lbnRGZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6K+E5Lyw6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJhcHByYWlzYWxGZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5omn6KGM6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJleGVjdXRpb25GZWUiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i6L+d57qm6YeRIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJwZW5hbHR5IiB3aWR0aD0iODAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaLheS/nei0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0iZ3VhcmFudGVlRmVlIiB3aWR0aD0iODAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWxhemXtOi0uSIgYWxpZ249ImNlbnRlciIgcHJvcD0iaW50ZXJtZWRpYXJ5RmVlIiB3aWR0aD0iODAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuS7o+WBv+mHkSIgYWxpZ249ImNlbnRlciIgcHJvcD0iY29tcGVuc2l0eSIgd2lkdGg9IjgwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLliKTlhrPph5Hpop0iIGFsaWduPSJjZW50ZXIiIHByb3A9Imp1ZGdtZW50QW1vdW50IiB3aWR0aD0iMTAwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLliKnmga8iIGFsaWduPSJjZW50ZXIiIHByb3A9ImludGVyZXN0IiB3aWR0aD0iODAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWFtuS7luasoOasviIgYWxpZ249ImNlbnRlciIgcHJvcD0ib3RoZXJBbW91bnRzT3dlZCIgd2lkdGg9IjEwMCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5L+d6Zmp6LS5IiBhbGlnbj0iY2VudGVyIiBwcm9wPSJpbnN1cmFuY2UiIHdpZHRoPSI4MCIgLz4KICAgICAgPGVsLXRhYmxlLWNvbHVtbiBsYWJlbD0i5oC76LS555SoIiBhbGlnbj0iY2VudGVyIiBwcm9wPSJ0b3RhbE1vbmV5IiB3aWR0aD0iMTAwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLlrqHmibnnirbmgIEiIGFsaWduPSJjZW50ZXIiIHByb3A9ImFwcHJvdmFsU3RhdHVzIiB3aWR0aD0iMTAwIj4KICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgPGVsLXRhZyB2LWlmPSJzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gbnVsbCB8fCBzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gJyciIHR5cGU9ImluZm8iPuacquWuoeaguDwvZWwtdGFnPgogICAgICAgICAgPGVsLXRhZyB2LWVsc2UtaWY9InNjb3BlLnJvdy5hcHByb3ZhbFN0YXR1cyA9PSAnMCciIHR5cGU9InN1Y2Nlc3MiPuW3sumAmui/hzwvZWwtdGFnPgogICAgICAgICAgPGVsLXRhZyB2LWVsc2UtaWY9InNjb3BlLnJvdy5hcHByb3ZhbFN0YXR1cyA9PSAnMSciIHR5cGU9ImRhbmdlciI+5bey5ouS57udPC9lbC10YWc+CiAgICAgICAgPC90ZW1wbGF0ZT4KICAgICAgPC9lbC10YWJsZS1jb2x1bW4+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWuoeaJueaXtumXtCIgYWxpZ249ImNlbnRlciIgcHJvcD0iYXBwcm92ZVRpbWUiIHdpZHRoPSIxNTAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuWuoeaJueS6uiIgYWxpZ249ImNlbnRlciIgcHJvcD0iYXBwcm92ZUJ5IiB3aWR0aD0iMTAwIiAvPgogICAgICA8ZWwtdGFibGUtY29sdW1uIGxhYmVsPSLmi5Lnu53ljp/lm6AiIGFsaWduPSJjZW50ZXIiIHByb3A9InJlYXNvbnMiIHdpZHRoPSIxNTAiIC8+CiAgICAgIDxlbC10YWJsZS1jb2x1bW4gbGFiZWw9IuaTjeS9nCIgYWxpZ249ImNlbnRlciIgd2lkdGg9IjE1MCIgZml4ZWQ9InJpZ2h0Ij4KICAgICAgICA8dGVtcGxhdGUgc2xvdC1zY29wZT0ic2NvcGUiPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gbnVsbCB8fCBzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gJyciCiAgICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICAgIHR5cGU9InN1Y2Nlc3MiCiAgICAgICAgICAgIEBjbGljaz0iaGFuZGxlU2luZ2xlQXBwcm92ZShzY29wZS5yb3csICcwJykiPgogICAgICAgICAgICDpgJrov4cKICAgICAgICAgIDwvZWwtYnV0dG9uPgogICAgICAgICAgPGVsLWJ1dHRvbgogICAgICAgICAgICB2LWlmPSJzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gbnVsbCB8fCBzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gJyciCiAgICAgICAgICAgIHNpemU9Im1pbmkiCiAgICAgICAgICAgIHR5cGU9ImRhbmdlciIKICAgICAgICAgICAgQGNsaWNrPSJoYW5kbGVTaW5nbGVBcHByb3ZlKHNjb3BlLnJvdywgJzEnKSI+CiAgICAgICAgICAgIOaLkue7nQogICAgICAgICAgPC9lbC1idXR0b24+CiAgICAgICAgICA8ZGl2IHYtZWxzZT4KICAgICAgICAgICAgPGVsLXRhZyB2LWlmPSJzY29wZS5yb3cuYXBwcm92YWxTdGF0dXMgPT0gJzAnIiB0eXBlPSJzdWNjZXNzIiBzaXplPSJtaW5pIj7lt7LpgJrov4c8L2VsLXRhZz4KICAgICAgICAgICAgPGVsLXRhZyB2LWVsc2UtaWY9InNjb3BlLnJvdy5hcHByb3ZhbFN0YXR1cyA9PSAnMSciIHR5cGU9ImRhbmdlciIgc2l6ZT0ibWluaSI+5bey5ouS57udPC9lbC10YWc+CiAgICAgICAgICAgIDxkaXYgdi1pZj0ic2NvcGUucm93LmFwcHJvdmVCeSIgc3R5bGU9ImZvbnQtc2l6ZTogMTJweDsgY29sb3I6ICM5OTk7IG1hcmdpbi10b3A6IDJweDsiPgogICAgICAgICAgICAgIHt7IHNjb3BlLnJvdy5hcHByb3ZlQnkgfX0KICAgICAgICAgICAgPC9kaXY+CiAgICAgICAgICAgIDxkaXYgdi1pZj0ic2NvcGUucm93LmFwcHJvdmVUaW1lIiBzdHlsZT0iZm9udC1zaXplOiAxMnB4OyBjb2xvcjogIzk5OTsiPgogICAgICAgICAgICAgIHt7IHNjb3BlLnJvdy5hcHByb3ZlVGltZSB9fQogICAgICAgICAgICA8L2Rpdj4KICAgICAgICAgIDwvZGl2PgogICAgICAgIDwvdGVtcGxhdGU+CiAgICAgIDwvZWwtdGFibGUtY29sdW1uPgogICAgPC9lbC10YWJsZT4KCiAgICA8ZGl2IHNsb3Q9ImZvb3RlciIgY2xhc3M9ImRpYWxvZy1mb290ZXIiPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0iY2FuY2VsIj7lhbMg6ZetPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSDljZXkuKrlrqHmibnnoa7orqTlr7nor53moYYgLS0+CiAgPGVsLWRpYWxvZyB0aXRsZT0i5a6h5om556Gu6K6kIiA6dmlzaWJsZS5zeW5jPSJzaW5nbGVBcHByb3ZhbE9wZW4iIHdpZHRoPSI0MDBweCIgYXBwZW5kLXRvLWJvZHk+CiAgICA8ZWwtZm9ybSByZWY9InNpbmdsZUFwcHJvdmFsRm9ybSIgOm1vZGVsPSJzaW5nbGVBcHByb3ZhbEZvcm0iIGxhYmVsLXdpZHRoPSI4MHB4Ij4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5a6h5om557uT5p6cIj4KICAgICAgICA8ZWwtdGFnIHYtaWY9InNpbmdsZUFwcHJvdmFsRm9ybS5zdGF0dXMgPT0gJzAnIiB0eXBlPSJzdWNjZXNzIj7pgJrov4c8L2VsLXRhZz4KICAgICAgICA8ZWwtdGFnIHYtZWxzZSB0eXBlPSJkYW5nZXIiPuaLkue7nTwvZWwtdGFnPgogICAgICA8L2VsLWZvcm0taXRlbT4KICAgICAgPGVsLWZvcm0taXRlbSBsYWJlbD0i5ouS57ud5Y6f5ZugIiB2LWlmPSJzaW5nbGVBcHByb3ZhbEZvcm0uc3RhdHVzID09ICcxJyI+CiAgICAgICAgPGVsLWlucHV0CiAgICAgICAgICB0eXBlPSJ0ZXh0YXJlYSIKICAgICAgICAgIDpyb3dzPSIzIgogICAgICAgICAgcGxhY2Vob2xkZXI9Iuivt+i+k+WFpeaLkue7neWOn+WboCIKICAgICAgICAgIHYtbW9kZWw9InNpbmdsZUFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24iPgogICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImNvbmZpcm1TaW5nbGVBcHByb3ZhbCI+56GuIOWumjwvZWwtYnV0dG9uPgogICAgICA8ZWwtYnV0dG9uIEBjbGljaz0ic2luZ2xlQXBwcm92YWxPcGVuID0gZmFsc2UiPuWPliDmtog8L2VsLWJ1dHRvbj4KICAgIDwvZGl2PgogIDwvZWwtZGlhbG9nPgoKICA8IS0tIOaJuemHj+WuoeaJueehruiupOWvueivneahhiAtLT4KICA8ZWwtZGlhbG9nIHRpdGxlPSLmibnph4/lrqHmibnnoa7orqQiIDp2aXNpYmxlLnN5bmM9ImJhdGNoQXBwcm92YWxPcGVuIiB3aWR0aD0iNDAwcHgiIGFwcGVuZC10by1ib2R5PgogICAgPGVsLWZvcm0gcmVmPSJiYXRjaEFwcHJvdmFsRm9ybSIgOm1vZGVsPSJiYXRjaEFwcHJvdmFsRm9ybSIgbGFiZWwtd2lkdGg9IjgwcHgiPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLlrqHmibnnu5PmnpwiPgogICAgICAgIDxlbC10YWcgdi1pZj0iYmF0Y2hBcHByb3ZhbEZvcm0uc3RhdHVzID09ICcwJyIgdHlwZT0ic3VjY2VzcyI+5om56YeP6YCa6L+HPC9lbC10YWc+CiAgICAgICAgPGVsLXRhZyB2LWVsc2UgdHlwZT0iZGFuZ2VyIj7mibnph4/mi5Lnu508L2VsLXRhZz4KICAgICAgPC9lbC1mb3JtLWl0ZW0+CiAgICAgIDxlbC1mb3JtLWl0ZW0gbGFiZWw9IumAieS4reiusOW9lSI+CiAgICAgICAgPHNwYW4+e3sgc2VsZWN0ZWRSZWNvcmRzLmxlbmd0aCB9fSDmnaHorrDlvZU8L3NwYW4+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgICA8ZWwtZm9ybS1pdGVtIGxhYmVsPSLmi5Lnu53ljp/lm6AiIHYtaWY9ImJhdGNoQXBwcm92YWxGb3JtLnN0YXR1cyA9PSAnMSciPgogICAgICAgIDxlbC1pbnB1dAogICAgICAgICAgdHlwZT0idGV4dGFyZWEiCiAgICAgICAgICA6cm93cz0iMyIKICAgICAgICAgIHBsYWNlaG9sZGVyPSLor7fovpPlhaXmi5Lnu53ljp/lm6AiCiAgICAgICAgICB2LW1vZGVsPSJiYXRjaEFwcHJvdmFsRm9ybS5yZWplY3RSZWFzb24iPgogICAgICAgIDwvZWwtaW5wdXQ+CiAgICAgIDwvZWwtZm9ybS1pdGVtPgogICAgPC9lbC1mb3JtPgogICAgPGRpdiBzbG90PSJmb290ZXIiIGNsYXNzPSJkaWFsb2ctZm9vdGVyIj4KICAgICAgPGVsLWJ1dHRvbiB0eXBlPSJwcmltYXJ5IiBAY2xpY2s9ImNvbmZpcm1CYXRjaEFwcHJvdmFsIj7noa4g5a6aPC9lbC1idXR0b24+CiAgICAgIDxlbC1idXR0b24gQGNsaWNrPSJiYXRjaEFwcHJvdmFsT3BlbiA9IGZhbHNlIj7lj5Yg5raIPC9lbC1idXR0b24+CiAgICA8L2Rpdj4KICA8L2VsLWRpYWxvZz4KCiAgPCEtLSDotLfmrL7kurrkv6Hmga/lr7nor53moYYgLS0+CiAgPHVzZXJJbmZvIHJlZj0idXNlckluZm8iIDp2aXNpYmxlLnN5bmM9InVzZXJJbmZvVmlzaWJsZSIgdGl0bGU9Iui0t+asvuS6uuS/oeaBryIgOmN1c3RvbWVySW5mbz0iY3VzdG9tZXJJbmZvIiAvPgo8L2Rpdj4K"}, null]}